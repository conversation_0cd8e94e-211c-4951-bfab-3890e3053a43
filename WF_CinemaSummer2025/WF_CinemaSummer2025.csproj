<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{684B2110-3151-4C09-A3F7-136E6E79DAE4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>WF_CinemaSummer2025</RootNamespace>
    <AssemblyName>WF_CinemaSummer2025</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BCrypt.Net-Next, Version=4.0.3.0, Culture=neutral, PublicKeyToken=1e11be04b6288443, processorArchitecture=MSIL">
      <HintPath>..\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Guna.UI2, Version=2.0.4.7, Culture=neutral, PublicKeyToken=8b9d14aa5142e261, processorArchitecture=MSIL">
      <HintPath>..\packages\Guna.UI2.WinForms.2.0.4.7\lib\net48\Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repositories\customer.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\DatabaseModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DatabaseModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\DatabaseModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\DatabaseModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DatabaseModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="Repositories\movie_attributes.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\movie_types.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\movy.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\price.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\room.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\room_sections.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\schedule.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\sp_helpdiagramdefinition_Result.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\sp_helpdiagrams_Result.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\sysdiagram.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\ticket.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\type.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Repositories\user.cs">
      <DependentUpon>DatabaseModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Services\DatabaseSeeder.cs" />
    <Compile Include="Services\RoomService.cs" />
    <Compile Include="Services\UserService.cs" />
    <Compile Include="UIs\Auth\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UIs\Auth\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UIs\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UIs\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UIs\Room\AddRoomForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UIs\Room\AddRoomForm.Designer.cs">
      <DependentUpon>AddRoomForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UIs\Room\RoomListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UIs\Room\RoomListForm.Designer.cs">
      <DependentUpon>RoomListForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UIs\Room\RoomListUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UIs\Room\RoomListUserControl.Designer.cs">
      <DependentUpon>RoomListUserControl.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UIs\Auth\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UIs\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UIs\Room\AddRoomForm.resx">
      <DependentUpon>AddRoomForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UIs\Room\RoomListForm.resx">
      <DependentUpon>RoomListForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UIs\Room\RoomListUserControl.resx">
      <DependentUpon>RoomListUserControl.cs</DependentUpon>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="app.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EntityDeploy Include="Repositories\DatabaseModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>DatabaseModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="Repositories\DatabaseModel.edmx.diagram">
      <DependentUpon>DatabaseModel.edmx</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Repositories\DatabaseModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DatabaseModel.edmx</DependentUpon>
      <LastGenOutput>DatabaseModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Repositories\DatabaseModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DatabaseModel.edmx</DependentUpon>
      <LastGenOutput>DatabaseModel.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>