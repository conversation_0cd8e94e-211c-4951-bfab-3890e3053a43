using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class RoomListUserControl : UserControl
    {
        private RoomService roomService;
        private user currentUser;
        
        // Pagination variables
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalRecords = 0;
        private int totalPages = 0;
        private System.Windows.Forms.Timer resizeTimer;

        public RoomListUserControl()
        {
            InitializeComponent();
        }

        public void Initialize(user loggedInUser)
        {
            roomService = new RoomService();
            currentUser = loggedInUser;
            
            InitializeUI();
            LoadRoomData();
        }

        private void InitializeUI()
        {
            // Setup DataGridView
            SetupDataGridView();

            // Setup ComboBox filter
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("Tất cả");
            cmbStatusFilter.Items.Add("Hoạt động");
            cmbStatusFilter.Items.Add("Bảo trì");
            cmbStatusFilter.Items.Add("Ngừng hoạt động");
            cmbStatusFilter.SelectedIndex = 0;

            // Setup page size combo
            cmbPageSize.Items.Clear();
            cmbPageSize.Items.AddRange(new object[] { 5, 10, 20, 50 });
            cmbPageSize.SelectedItem = pageSize;

            // Initialize pagination controls
            UpdatePaginationControls();

            // Setup responsive layout
            SetupResponsiveLayout();

            // Handle resize event
            this.Resize += RoomListUserControl_Resize;
        }

        private void SetupResponsiveLayout()
        {
            // Ensure proper anchoring for responsive design
            AdjustControlsForWidth();
        }

        private void RoomListUserControl_Resize(object sender, EventArgs e)
        {
            AdjustControlsForWidth();
        }

        private void AdjustControlsForWidth()
        {
            try
            {
                if (panelControls == null || !this.IsHandleCreated) return;

                // Use panel width for calculations
                int availableWidth = panelControls.Width;
                if (availableWidth < 100) return; // Too small to work with

                // Debug output
                System.Diagnostics.Debug.WriteLine($"Panel width: {availableWidth}");

                // Always show search and filter with fixed positions
                txtSearch.Location = new Point(100, 25);
                txtSearch.Size = new Size(150, 36);

                label2.Location = new Point(270, 35);
                cmbStatusFilter.Location = new Point(340, 25);
                cmbStatusFilter.Size = new Size(100, 36);
                cmbStatusFilter.Visible = true;
                label2.Visible = true;

                // Calculate available space for buttons
                int buttonAreaStart = 460; // After filter area
                int buttonAreaWidth = availableWidth - buttonAreaStart - 20; // 20px right margin

                System.Diagnostics.Debug.WriteLine($"Button area width: {buttonAreaWidth}");

                // Force show text for debugging - always use full text
                btnRefresh.Text = "🔄 Làm mới";
                btnAdd.Text = "➕ Thêm";
                btnEdit.Text = "✏️ Sửa";
                btnDelete.Text = "🗑️ Xóa";

                btnRefresh.Size = new Size(100, 35);
                btnAdd.Size = new Size(85, 35);
                btnEdit.Size = new Size(75, 35);
                btnDelete.Size = new Size(75, 35);

                // Position buttons from right to left with proper spacing
                int rightMargin = 20;
                int buttonSpacing = 10;
                int currentX = availableWidth - rightMargin;

                btnDelete.Location = new Point(currentX - btnDelete.Width, 25);
                currentX = btnDelete.Left - buttonSpacing;

                btnEdit.Location = new Point(currentX - btnEdit.Width, 25);
                currentX = btnEdit.Left - buttonSpacing;

                btnAdd.Location = new Point(currentX - btnAdd.Width, 25);
                currentX = btnAdd.Left - buttonSpacing;

                btnRefresh.Location = new Point(currentX - btnRefresh.Width, 25);

                System.Diagnostics.Debug.WriteLine($"Button positions - Refresh: {btnRefresh.Location}, Add: {btnAdd.Location}, Edit: {btnEdit.Location}, Delete: {btnDelete.Location}");

                // If buttons overlap with filter, hide filter
                if (btnRefresh.Left < cmbStatusFilter.Right + 20)
                {
                    cmbStatusFilter.Visible = false;
                    label2.Visible = false;
                    System.Diagnostics.Debug.WriteLine("Filter hidden due to overlap");
                }

                // Force button refresh
                btnRefresh.Refresh();
                btnAdd.Refresh();
                btnEdit.Refresh();
                btnDelete.Refresh();

                // Adjust pagination controls
                AdjustPaginationControls();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Layout adjustment error: {ex.Message}");
            }
        }

        private void AdjustPaginationControls()
        {
            try
            {
                if (panelPagination == null) return;

                int panelWidth = panelPagination.Width;
                if (panelWidth < 100) return;

                System.Diagnostics.Debug.WriteLine($"Pagination panel width: {panelWidth}");

                // Page info on the left
                lblPageInfo.Location = new Point(15, 15);

                // Always show all controls first
                lblPageSize.Visible = true;
                cmbPageSize.Visible = true;
                btnFirstPage.Visible = true;
                btnLastPage.Visible = true;
                btnPrevPage.Visible = true;
                btnNextPage.Visible = true;
                txtCurrentPage.Visible = true;

                // Calculate available space for pagination controls
                int rightMargin = 20;
                int spacing = 8;

                // Force responsive layout based on panel width
                if (panelWidth < 600)
                {
                    // Very small - minimal pagination
                    lblPageSize.Visible = false;
                    cmbPageSize.Visible = false;
                    btnFirstPage.Visible = false;
                    btnLastPage.Visible = false;

                    // Only show Prev | Current | Next
                    int currentX = panelWidth - rightMargin;

                    btnNextPage.Location = new Point(currentX - btnNextPage.Width, 10);
                    currentX = btnNextPage.Left - spacing;

                    txtCurrentPage.Location = new Point(currentX - txtCurrentPage.Width, 10);
                    currentX = txtCurrentPage.Left - spacing;

                    btnPrevPage.Location = new Point(currentX - btnPrevPage.Width, 10);

                    System.Diagnostics.Debug.WriteLine("Minimal pagination layout applied");
                }
                else if (panelWidth < 800)
                {
                    // Medium - show navigation but hide page size
                    lblPageSize.Visible = false;
                    cmbPageSize.Visible = false;

                    // Show First | Prev | Current | Next | Last
                    int currentX = panelWidth - rightMargin;

                    btnLastPage.Location = new Point(currentX - btnLastPage.Width, 10);
                    currentX = btnLastPage.Left - spacing;

                    btnNextPage.Location = new Point(currentX - btnNextPage.Width, 10);
                    currentX = btnNextPage.Left - spacing;

                    txtCurrentPage.Location = new Point(currentX - txtCurrentPage.Width, 10);
                    currentX = txtCurrentPage.Left - spacing;

                    btnPrevPage.Location = new Point(currentX - btnPrevPage.Width, 10);
                    currentX = btnPrevPage.Left - spacing;

                    btnFirstPage.Location = new Point(currentX - btnFirstPage.Width, 10);

                    System.Diagnostics.Debug.WriteLine("Medium pagination layout applied");
                }
                else
                {
                    // Full - show everything
                    int currentX = panelWidth - rightMargin;

                    cmbPageSize.Location = new Point(currentX - cmbPageSize.Width, 10);
                    currentX = cmbPageSize.Left - 10;

                    lblPageSize.Location = new Point(currentX - lblPageSize.Width, 17);
                    currentX = lblPageSize.Left - 20;

                    btnLastPage.Location = new Point(currentX - btnLastPage.Width, 10);
                    currentX = btnLastPage.Left - spacing;

                    btnNextPage.Location = new Point(currentX - btnNextPage.Width, 10);
                    currentX = btnNextPage.Left - spacing;

                    txtCurrentPage.Location = new Point(currentX - txtCurrentPage.Width, 10);
                    currentX = txtCurrentPage.Left - spacing;

                    btnPrevPage.Location = new Point(currentX - btnPrevPage.Width, 10);
                    currentX = btnPrevPage.Left - spacing;

                    btnFirstPage.Location = new Point(currentX - btnFirstPage.Width, 10);

                    System.Diagnostics.Debug.WriteLine("Full pagination layout applied");
                }

                // Force refresh all pagination controls
                lblPageSize.Refresh();
                cmbPageSize.Refresh();
                btnFirstPage.Refresh();
                btnPrevPage.Refresh();
                txtCurrentPage.Refresh();
                btnNextPage.Refresh();
                btnLastPage.Refresh();

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pagination layout error: {ex.Message}");
            }
        }

        private void SetupDataGridView()
        {
            dgvRooms.AutoGenerateColumns = false;
            dgvRooms.AllowUserToAddRows = false;
            dgvRooms.AllowUserToDeleteRows = false;
            dgvRooms.ReadOnly = true;
            dgvRooms.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvRooms.MultiSelect = false;
            
            // Clear existing columns
            dgvRooms.Columns.Clear();
            
            // Add columns
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RoomName",
                HeaderText = "Tên Phòng",
                DataPropertyName = "room_name",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("Segoe UI", 10, FontStyle.Bold) }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Mô Tả",
                DataPropertyName = "description",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Capacity",
                HeaderText = "Sức Chứa",
                DataPropertyName = "capacity",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Trạng Thái",
                DataPropertyName = "status",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "Ngày Tạo",
                DataPropertyName = "created_at",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "dd/MM/yyyy HH:mm",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
            
            // Style the DataGridView
            dgvRooms.BackgroundColor = Color.White;
            dgvRooms.BorderStyle = BorderStyle.None;
            dgvRooms.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvRooms.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvRooms.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            dgvRooms.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvRooms.ColumnHeadersHeight = 40;
            dgvRooms.RowTemplate.Height = 35;
            dgvRooms.EnableHeadersVisualStyles = false;
        }

        private void LoadRoomData()
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string statusFilter = cmbStatusFilter.SelectedItem?.ToString() ?? "Tất cả";
                
                var result = roomService.GetRoomsWithPaging(currentPage, pageSize, searchText, statusFilter);
                dgvRooms.DataSource = result.rooms;
                
                totalRecords = result.totalRecords;
                totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);
                
                // Update statistics (get all rooms for accurate stats)
                var allRooms = roomService.GetAllRooms();
                UpdateStatistics(allRooms);
                
                // Update pagination controls
                UpdatePaginationControls();
                
                // Apply row colors based on status
                ApplyRowColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdatePaginationControls()
        {
            // Update page info label
            lblPageInfo.Text = $"Trang {currentPage} / {Math.Max(totalPages, 1)} (Tổng: {totalRecords} bản ghi)";
            
            // Update button states
            btnFirstPage.Enabled = currentPage > 1;
            btnPrevPage.Enabled = currentPage > 1;
            btnNextPage.Enabled = currentPage < totalPages;
            btnLastPage.Enabled = currentPage < totalPages;
            
            // Update current page textbox
            txtCurrentPage.Text = currentPage.ToString();
        }

        private void UpdateStatistics(System.Collections.Generic.List<room> rooms)
        {
            lblTotalRooms.Text = $"Tổng số phòng: {rooms.Count}";
            lblActiveRooms.Text = $"Hoạt động: {rooms.Count(r => r.status == "Hoạt động")}";
            lblMaintenanceRooms.Text = $"Bảo trì: {rooms.Count(r => r.status == "Bảo trì")}";
        }

        private void ApplyRowColors()
        {
            foreach (DataGridViewRow row in dgvRooms.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "Hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(46, 204, 113);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Bảo trì":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(241, 196, 15);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Ngừng hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(231, 76, 60);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                    }
                }
            }
        }

        // Event handlers
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadRoomData();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng thêm phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần chỉnh sửa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("Chức năng chỉnh sửa phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần xóa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("Bạn có chắc chắn muốn xóa phòng này?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var selectedRoom = (room)dgvRooms.SelectedRows[0].DataBoundItem;
                    roomService.DeleteRoom(selectedRoom.room_id);
                    
                    MessageBox.Show("Xóa phòng thành công!", "Thành công", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadRoomData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi khi xóa phòng: {ex.Message}", "Lỗi", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        // Pagination event handlers
        private void btnFirstPage_Click(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void btnPrevPage_Click(object sender, EventArgs e)
        {
            if (currentPage > 1)
            {
                currentPage--;
                LoadRoomData();
            }
        }

        private void btnNextPage_Click(object sender, EventArgs e)
        {
            if (currentPage < totalPages)
            {
                currentPage++;
                LoadRoomData();
            }
        }

        private void btnLastPage_Click(object sender, EventArgs e)
        {
            currentPage = totalPages;
            LoadRoomData();
        }

        private void txtCurrentPage_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (int.TryParse(txtCurrentPage.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= totalPages)
                    {
                        currentPage = pageNumber;
                        LoadRoomData();
                    }
                    else
                    {
                        MessageBox.Show($"Số trang phải từ 1 đến {totalPages}!", "Thông báo", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCurrentPage.Text = currentPage.ToString();
                    }
                }
                else
                {
                    txtCurrentPage.Text = currentPage.ToString();
                }
            }
        }

        private void cmbPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(cmbPageSize.SelectedItem.ToString(), out int newPageSize))
            {
                pageSize = newPageSize;
                currentPage = 1;
                LoadRoomData();
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (roomService == null && currentUser != null)
            {
                Initialize(currentUser);
            }

            // Force layout adjustment after load with multiple attempts
            this.BeginInvoke(new Action(() => {
                AdjustControlsForWidth();

                // Second attempt after a short delay to ensure everything is loaded
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 200;
                timer.Tick += (s, args) => {
                    timer.Stop();
                    timer.Dispose();
                    AdjustControlsForWidth();
                };
                timer.Start();
            }));
        }

        // Public method to force layout refresh
        public void RefreshLayout()
        {
            if (this.IsHandleCreated)
            {
                AdjustControlsForWidth();
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            if (this.IsHandleCreated && this.Visible)
            {
                // Use timer to debounce rapid resize events
                if (resizeTimer != null)
                {
                    resizeTimer.Stop();
                    resizeTimer.Dispose();
                }

                resizeTimer = new System.Windows.Forms.Timer();
                resizeTimer.Interval = 50; // 50ms delay
                resizeTimer.Tick += (s, args) => {
                    resizeTimer.Stop();
                    resizeTimer.Dispose();
                    resizeTimer = null;
                    AdjustControlsForWidth();
                };
                resizeTimer.Start();
            }
        }

        protected override void OnParentChanged(EventArgs e)
        {
            base.OnParentChanged(e);
            if (this.Parent != null)
            {
                // Adjust when added to parent
                this.BeginInvoke(new Action(() => {
                    AdjustControlsForWidth();
                }));
            }
        }

        protected override void OnVisibleChanged(EventArgs e)
        {
            base.OnVisibleChanged(e);
            if (this.Visible && this.IsHandleCreated)
            {
                // Adjust when becoming visible
                this.BeginInvoke(new Action(() => {
                    AdjustControlsForWidth();
                }));
            }
        }

        public void Cleanup()
        {
            resizeTimer?.Stop();
            resizeTimer?.Dispose();
            resizeTimer = null;
            roomService?.Dispose();
        }
    }
}
