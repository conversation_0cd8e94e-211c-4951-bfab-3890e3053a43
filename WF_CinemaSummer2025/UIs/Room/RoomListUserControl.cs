using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class RoomListUserControl : UserControl
    {
        private RoomService roomService;
        private user currentUser;
        
        // Pagination variables
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalRecords = 0;
        private int totalPages = 0;

        public RoomListUserControl()
        {
            InitializeComponent();
        }

        public void Initialize(user loggedInUser)
        {
            roomService = new RoomService();
            currentUser = loggedInUser;
            
            InitializeUI();
            LoadRoomData();
        }

        private void InitializeUI()
        {
            // Setup DataGridView
            SetupDataGridView();

            // Setup ComboBox filter
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("Tất cả");
            cmbStatusFilter.Items.Add("Hoạt động");
            cmbStatusFilter.Items.Add("Bảo trì");
            cmbStatusFilter.Items.Add("Ngừng hoạt động");
            cmbStatusFilter.SelectedIndex = 0;

            // Setup page size combo
            cmbPageSize.Items.Clear();
            cmbPageSize.Items.AddRange(new object[] { 5, 10, 20, 50 });
            cmbPageSize.SelectedItem = pageSize;

            // Initialize pagination controls
            UpdatePaginationControls();

            // Setup responsive layout
            SetupResponsiveLayout();

            // Handle resize event
            this.Resize += RoomListUserControl_Resize;
        }

        private void SetupResponsiveLayout()
        {
            // Ensure proper anchoring for responsive design
            AdjustControlsForWidth();
        }

        private void RoomListUserControl_Resize(object sender, EventArgs e)
        {
            AdjustControlsForWidth();
        }

        private void AdjustControlsForWidth()
        {
            try
            {
                int panelWidth = panelControls.Width;

                if (panelWidth < 500)
                {
                    // Very small - hide filter, stack vertically if needed
                    cmbStatusFilter.Visible = false;
                    label2.Visible = false;

                    // Adjust search box
                    txtSearch.Size = new Size(120, 36);
                    txtSearch.Location = new Point(100, 25);

                    // Small buttons with text
                    btnRefresh.Text = "Refresh";
                    btnAdd.Text = "Thêm";
                    btnEdit.Text = "Sửa";
                    btnDelete.Text = "Xóa";

                    btnRefresh.Size = new Size(60, 35);
                    btnAdd.Size = new Size(60, 35);
                    btnEdit.Size = new Size(60, 35);
                    btnDelete.Size = new Size(60, 35);
                }
                else if (panelWidth < 700)
                {
                    // Medium - show filter but smaller
                    cmbStatusFilter.Visible = true;
                    label2.Visible = true;

                    txtSearch.Size = new Size(140, 36);
                    txtSearch.Location = new Point(100, 25);

                    cmbStatusFilter.Size = new Size(80, 36);

                    btnRefresh.Text = "Refresh";
                    btnAdd.Text = "Thêm";
                    btnEdit.Text = "Sửa";
                    btnDelete.Text = "Xóa";

                    btnRefresh.Size = new Size(65, 35);
                    btnAdd.Size = new Size(65, 35);
                    btnEdit.Size = new Size(65, 35);
                    btnDelete.Size = new Size(65, 35);
                }
                else
                {
                    // Full width - show everything normal
                    cmbStatusFilter.Visible = true;
                    label2.Visible = true;

                    txtSearch.Size = new Size(180, 36);
                    txtSearch.Location = new Point(100, 25);

                    cmbStatusFilter.Size = new Size(120, 36);

                    btnRefresh.Text = "🔄 Refresh";
                    btnAdd.Text = "➕ Thêm";
                    btnEdit.Text = "✏️ Sửa";
                    btnDelete.Text = "🗑️ Xóa";

                    btnRefresh.Size = new Size(85, 35);
                    btnAdd.Size = new Size(85, 35);
                    btnEdit.Size = new Size(85, 35);
                    btnDelete.Size = new Size(85, 35);
                }

                // Calculate positions from right to left with proper spacing
                int rightMargin = 20;
                int buttonSpacing = 8;
                int currentX = panelWidth - rightMargin;

                // Position buttons from right to left
                btnDelete.Location = new Point(currentX - btnDelete.Width, 25);
                currentX = btnDelete.Left - buttonSpacing;

                btnEdit.Location = new Point(currentX - btnEdit.Width, 25);
                currentX = btnEdit.Left - buttonSpacing;

                btnAdd.Location = new Point(currentX - btnAdd.Width, 25);
                currentX = btnAdd.Left - buttonSpacing;

                btnRefresh.Location = new Point(currentX - btnRefresh.Width, 25);

                // Position filter controls if visible
                if (cmbStatusFilter.Visible)
                {
                    // Leave space between search area and buttons
                    int filterX = Math.Min(btnRefresh.Left - 30, txtSearch.Right + 50);
                    cmbStatusFilter.Location = new Point(filterX - cmbStatusFilter.Width, 25);

                    // Position label to the left of combo
                    label2.Location = new Point(cmbStatusFilter.Left - label2.Width - 8, 35);

                    // Make sure label doesn't overlap with search
                    if (label2.Left < txtSearch.Right + 10)
                    {
                        label2.Location = new Point(txtSearch.Right + 10, 35);
                        cmbStatusFilter.Location = new Point(label2.Right + 8, 25);
                    }
                }
            }
            catch (Exception ex)
            {
                // Fallback to prevent crashes
                System.Diagnostics.Debug.WriteLine($"Layout adjustment error: {ex.Message}");
            }
        }

        private void SetupDataGridView()
        {
            dgvRooms.AutoGenerateColumns = false;
            dgvRooms.AllowUserToAddRows = false;
            dgvRooms.AllowUserToDeleteRows = false;
            dgvRooms.ReadOnly = true;
            dgvRooms.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvRooms.MultiSelect = false;
            
            // Clear existing columns
            dgvRooms.Columns.Clear();
            
            // Add columns
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RoomName",
                HeaderText = "Tên Phòng",
                DataPropertyName = "room_name",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("Segoe UI", 10, FontStyle.Bold) }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Mô Tả",
                DataPropertyName = "description",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Capacity",
                HeaderText = "Sức Chứa",
                DataPropertyName = "capacity",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Trạng Thái",
                DataPropertyName = "status",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "Ngày Tạo",
                DataPropertyName = "created_at",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "dd/MM/yyyy HH:mm",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
            
            // Style the DataGridView
            dgvRooms.BackgroundColor = Color.White;
            dgvRooms.BorderStyle = BorderStyle.None;
            dgvRooms.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvRooms.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvRooms.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            dgvRooms.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvRooms.ColumnHeadersHeight = 40;
            dgvRooms.RowTemplate.Height = 35;
            dgvRooms.EnableHeadersVisualStyles = false;
        }

        private void LoadRoomData()
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string statusFilter = cmbStatusFilter.SelectedItem?.ToString() ?? "Tất cả";
                
                var result = roomService.GetRoomsWithPaging(currentPage, pageSize, searchText, statusFilter);
                dgvRooms.DataSource = result.rooms;
                
                totalRecords = result.totalRecords;
                totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);
                
                // Update statistics (get all rooms for accurate stats)
                var allRooms = roomService.GetAllRooms();
                UpdateStatistics(allRooms);
                
                // Update pagination controls
                UpdatePaginationControls();
                
                // Apply row colors based on status
                ApplyRowColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdatePaginationControls()
        {
            // Update page info label
            lblPageInfo.Text = $"Trang {currentPage} / {Math.Max(totalPages, 1)} (Tổng: {totalRecords} bản ghi)";
            
            // Update button states
            btnFirstPage.Enabled = currentPage > 1;
            btnPrevPage.Enabled = currentPage > 1;
            btnNextPage.Enabled = currentPage < totalPages;
            btnLastPage.Enabled = currentPage < totalPages;
            
            // Update current page textbox
            txtCurrentPage.Text = currentPage.ToString();
        }

        private void UpdateStatistics(System.Collections.Generic.List<room> rooms)
        {
            lblTotalRooms.Text = $"Tổng số phòng: {rooms.Count}";
            lblActiveRooms.Text = $"Hoạt động: {rooms.Count(r => r.status == "Hoạt động")}";
            lblMaintenanceRooms.Text = $"Bảo trì: {rooms.Count(r => r.status == "Bảo trì")}";
        }

        private void ApplyRowColors()
        {
            foreach (DataGridViewRow row in dgvRooms.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "Hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(46, 204, 113);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Bảo trì":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(241, 196, 15);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Ngừng hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(231, 76, 60);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                    }
                }
            }
        }

        // Event handlers
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadRoomData();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Chức năng thêm phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần chỉnh sửa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("Chức năng chỉnh sửa phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần xóa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("Bạn có chắc chắn muốn xóa phòng này?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var selectedRoom = (room)dgvRooms.SelectedRows[0].DataBoundItem;
                    roomService.DeleteRoom(selectedRoom.room_id);
                    
                    MessageBox.Show("Xóa phòng thành công!", "Thành công", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadRoomData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi khi xóa phòng: {ex.Message}", "Lỗi", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        // Pagination event handlers
        private void btnFirstPage_Click(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void btnPrevPage_Click(object sender, EventArgs e)
        {
            if (currentPage > 1)
            {
                currentPage--;
                LoadRoomData();
            }
        }

        private void btnNextPage_Click(object sender, EventArgs e)
        {
            if (currentPage < totalPages)
            {
                currentPage++;
                LoadRoomData();
            }
        }

        private void btnLastPage_Click(object sender, EventArgs e)
        {
            currentPage = totalPages;
            LoadRoomData();
        }

        private void txtCurrentPage_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (int.TryParse(txtCurrentPage.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= totalPages)
                    {
                        currentPage = pageNumber;
                        LoadRoomData();
                    }
                    else
                    {
                        MessageBox.Show($"Số trang phải từ 1 đến {totalPages}!", "Thông báo", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCurrentPage.Text = currentPage.ToString();
                    }
                }
                else
                {
                    txtCurrentPage.Text = currentPage.ToString();
                }
            }
        }

        private void cmbPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(cmbPageSize.SelectedItem.ToString(), out int newPageSize))
            {
                pageSize = newPageSize;
                currentPage = 1;
                LoadRoomData();
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (roomService == null && currentUser != null)
            {
                Initialize(currentUser);
            }

            // Force layout adjustment after load
            this.BeginInvoke(new Action(() => {
                AdjustControlsForWidth();
            }));
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            AdjustControlsForWidth();
        }

        public void Cleanup()
        {
            roomService?.Dispose();
        }
    }
}
