using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class RoomListForm : Form
    {
        private RoomService roomService;
        private user currentUser;

        // Pagination variables
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalRecords = 0;
        private int totalPages = 0;

        public RoomListForm(user loggedInUser)
        {
            InitializeComponent();
            roomService = new RoomService();
            currentUser = loggedInUser;

            InitializeUI();
            LoadRoomData();

            // Setup responsive layout
            this.Load += RoomListForm_Load;
            this.Resize += RoomListForm_Resize;
        }

        private void RoomListForm_Load(object sender, EventArgs e)
        {
            AdjustControlsForWidth();
        }

        private void RoomListForm_Resize(object sender, EventArgs e)
        {
            AdjustControlsForWidth();
        }

        private void AdjustControlsForWidth()
        {
            try
            {
                if (panelControls == null || !this.IsHandleCreated) return;

                // Simple fixed layout - no complex responsive logic

                // Search area - left side
                txtSearch.Location = new Point(100, 25);
                txtSearch.Size = new Size(150, 36);

                // Filter area - middle
                label2.Location = new Point(270, 35);
                cmbStatusFilter.Location = new Point(340, 25);
                cmbStatusFilter.Size = new Size(100, 36);

                // Always show filter
                cmbStatusFilter.Visible = true;
                label2.Visible = true;

                // Buttons - right side with fixed positions
                btnRefresh.Text = "🔄 Refresh";
                btnAdd.Text = "➕ Thêm";
                btnEdit.Text = "✏️ Sửa";
                btnDelete.Text = "🗑️ Xóa";

                btnRefresh.Size = new Size(90, 35);
                btnAdd.Size = new Size(80, 35);
                btnEdit.Size = new Size(70, 35);
                btnDelete.Size = new Size(70, 35);

                // Fixed button positions from right
                int panelWidth = panelControls.Width;
                btnDelete.Location = new Point(panelWidth - 90, 25);
                btnEdit.Location = new Point(panelWidth - 170, 25);
                btnAdd.Location = new Point(panelWidth - 260, 25);
                btnRefresh.Location = new Point(panelWidth - 360, 25);

                // Adjust pagination
                AdjustPaginationControls();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Layout error: {ex.Message}");
            }
        }

        private void AdjustPaginationControls()
        {
            try
            {
                if (panelPagination == null) return;

                int panelWidth = panelPagination.Width;
                if (panelWidth < 100) return;

                // Simple fixed pagination layout

                // Page info on the left
                lblPageInfo.Location = new Point(15, 15);

                // Always show all controls
                lblPageSize.Visible = true;
                cmbPageSize.Visible = true;
                btnFirstPage.Visible = true;
                btnLastPage.Visible = true;
                btnPrevPage.Visible = true;
                btnNextPage.Visible = true;
                txtCurrentPage.Visible = true;

                // Fixed positions from right to left
                cmbPageSize.Location = new Point(panelWidth - 80, 10);
                lblPageSize.Location = new Point(panelWidth - 150, 17);

                btnLastPage.Location = new Point(panelWidth - 200, 10);
                btnNextPage.Location = new Point(panelWidth - 270, 10);
                txtCurrentPage.Location = new Point(panelWidth - 320, 10);
                btnPrevPage.Location = new Point(panelWidth - 390, 10);
                btnFirstPage.Location = new Point(panelWidth - 460, 10);

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pagination error: {ex.Message}");
            }
        }

        private void AddBackButton()
        {
            // Tạo nút quay lại
            var btnBack = new Guna.UI2.WinForms.Guna2Button();
            btnBack.Text = "← Quay lại";
            btnBack.Size = new Size(100, 35);
            btnBack.Location = new Point(10, 15);
            btnBack.FillColor = Color.FromArgb(94, 148, 255);
            btnBack.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnBack.ForeColor = Color.White;
            btnBack.BorderRadius = 8;
            btnBack.Click += BtnBack_Click;

            // Thêm vào panelTop
            panelTop.Controls.Add(btnBack);
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void InitializeUI()
        {
            this.Text = "Quản lý Phòng Chiếu";

            // Thêm nút quay lại
            AddBackButton();
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            
            // Setup DataGridView
            SetupDataGridView();
            
            // Setup ComboBox filter
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("Tất cả");
            cmbStatusFilter.Items.Add("Hoạt động");
            cmbStatusFilter.Items.Add("Bảo trì");
            cmbStatusFilter.Items.Add("Ngừng hoạt động");
            cmbStatusFilter.SelectedIndex = 0;

            // Setup page size combo
            cmbPageSize.Items.Clear();
            cmbPageSize.Items.AddRange(new object[] { 5, 10, 20, 50 });
            cmbPageSize.SelectedItem = pageSize;

            // Initialize pagination controls
            UpdatePaginationControls();
        }

        private void SetupDataGridView()
        {
            dgvRooms.AutoGenerateColumns = false;
            dgvRooms.AllowUserToAddRows = false;
            dgvRooms.AllowUserToDeleteRows = false;
            dgvRooms.ReadOnly = true;
            dgvRooms.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvRooms.MultiSelect = false;
            
            // Clear existing columns
            dgvRooms.Columns.Clear();
            
            // Add columns
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RoomName",
                HeaderText = "Tên Phòng",
                DataPropertyName = "room_name",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("Segoe UI", 10, FontStyle.Bold) }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Mô Tả",
                DataPropertyName = "description",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Capacity",
                HeaderText = "Sức Chứa",
                DataPropertyName = "capacity",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Trạng Thái",
                DataPropertyName = "status",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "Ngày Tạo",
                DataPropertyName = "created_at",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "dd/MM/yyyy HH:mm",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
            
            // Style the DataGridView
            dgvRooms.BackgroundColor = Color.White;
            dgvRooms.BorderStyle = BorderStyle.None;
            dgvRooms.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvRooms.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvRooms.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            dgvRooms.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvRooms.ColumnHeadersHeight = 40;
            dgvRooms.RowTemplate.Height = 35;
            dgvRooms.EnableHeadersVisualStyles = false;
        }

        private void LoadRoomData()
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string statusFilter = cmbStatusFilter.SelectedItem?.ToString() ?? "Tất cả";

                var result = roomService.GetRoomsWithPaging(currentPage, pageSize, searchText, statusFilter);
                dgvRooms.DataSource = result.rooms;

                totalRecords = result.totalRecords;
                totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // Update statistics (get all rooms for accurate stats)
                var allRooms = roomService.GetAllRooms();
                UpdateStatistics(allRooms);

                // Update pagination controls
                UpdatePaginationControls();

                // Apply row colors based on status
                ApplyRowColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationControls()
        {
            // Update page info label
            lblPageInfo.Text = $"Trang {currentPage} / {Math.Max(totalPages, 1)} (Tổng: {totalRecords} bản ghi)";

            // Update button states
            btnFirstPage.Enabled = currentPage > 1;
            btnPrevPage.Enabled = currentPage > 1;
            btnNextPage.Enabled = currentPage < totalPages;
            btnLastPage.Enabled = currentPage < totalPages;

            // Update current page textbox
            txtCurrentPage.Text = currentPage.ToString();
        }

        private void UpdateStatistics(System.Collections.Generic.List<room> rooms)
        {
            lblTotalRooms.Text = $"Tổng số phòng: {rooms.Count}";
            lblActiveRooms.Text = $"Hoạt động: {rooms.Count(r => r.status == "Hoạt động")}";
            lblMaintenanceRooms.Text = $"Bảo trì: {rooms.Count(r => r.status == "Bảo trì")}";
        }

        private void ApplyRowColors()
        {
            foreach (DataGridViewRow row in dgvRooms.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "Hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(46, 204, 113);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Bảo trì":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(241, 196, 15);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Ngừng hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(231, 76, 60);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                    }
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadRoomData();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            // TODO: Mở form thêm phòng mới
            MessageBox.Show("Chức năng thêm phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần chỉnh sửa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // TODO: Mở form chỉnh sửa phòng
            MessageBox.Show("Chức năng chỉnh sửa phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần xóa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("Bạn có chắc chắn muốn xóa phòng này?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var selectedRoom = (room)dgvRooms.SelectedRows[0].DataBoundItem;
                    roomService.DeleteRoom(selectedRoom.room_id);
                    
                    MessageBox.Show("Xóa phòng thành công!", "Thành công", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadRoomData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi khi xóa phòng: {ex.Message}", "Lỗi", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentPage = 1; // Reset to first page when filter changes
            LoadRoomData();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            currentPage = 1; // Reset to first page when search changes
            LoadRoomData();
        }

        // Pagination event handlers
        private void btnFirstPage_Click(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void btnPrevPage_Click(object sender, EventArgs e)
        {
            if (currentPage > 1)
            {
                currentPage--;
                LoadRoomData();
            }
        }

        private void btnNextPage_Click(object sender, EventArgs e)
        {
            if (currentPage < totalPages)
            {
                currentPage++;
                LoadRoomData();
            }
        }

        private void btnLastPage_Click(object sender, EventArgs e)
        {
            currentPage = totalPages;
            LoadRoomData();
        }

        private void txtCurrentPage_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (int.TryParse(txtCurrentPage.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= totalPages)
                    {
                        currentPage = pageNumber;
                        LoadRoomData();
                    }
                    else
                    {
                        MessageBox.Show($"Số trang phải từ 1 đến {totalPages}!", "Thông báo",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCurrentPage.Text = currentPage.ToString();
                    }
                }
                else
                {
                    txtCurrentPage.Text = currentPage.ToString();
                }
            }
        }

        private void cmbPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(cmbPageSize.SelectedItem.ToString(), out int newPageSize))
            {
                pageSize = newPageSize;
                currentPage = 1; // Reset to first page when page size changes
                LoadRoomData();
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            roomService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
