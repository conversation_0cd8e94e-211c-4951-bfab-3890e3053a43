using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class RoomListForm : Form
    {
        private RoomService roomService;
        private user currentUser;

        // Pagination variables
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalRecords = 0;
        private int totalPages = 0;

        public RoomListForm(user loggedInUser)
        {
            InitializeComponent();
            roomService = new RoomService();
            currentUser = loggedInUser;

            InitializeUI();
            LoadRoomData();
            // Setup responsive layout
            this.Load += RoomListForm_Load;
            this.Resize += RoomListForm_Resize;
        }

        private void RoomListForm_Load(object sender, EventArgs e)
        {
            // FlowLayoutPanel sẽ tự động handle layout
        }

        private void RoomListForm_Resize(object sender, EventArgs e)
        {
            // FlowLayoutPanel sẽ tự động handle layout
        }



        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void InitializeUI()
        {
            this.Text = "Quản lý Phòng Chiếu";

            // Thêm nút quay lại
            //AddBackButton();

            // Setup FlowLayoutPanel cho buttons
            //SetupButtonsFlowLayout();

            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            
            // Setup DataGridView
            SetupDataGridView();
            
            // Setup ComboBox filter
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("Tất cả");
            cmbStatusFilter.Items.Add("Hoạt động");
            cmbStatusFilter.Items.Add("Bảo trì");
            cmbStatusFilter.Items.Add("Ngừng hoạt động");
            cmbStatusFilter.SelectedIndex = 0;

            // Setup page size combo
            cmbPageSize.Items.Clear();
            cmbPageSize.Items.AddRange(new object[] { 5, 10, 20, 50 });
            cmbPageSize.SelectedItem = pageSize;
            // Initialize pagination controls
            UpdatePaginationControls();
        }

        private void SetupDataGridView()
        {
            dgvRooms.AutoGenerateColumns = false;
            dgvRooms.AllowUserToAddRows = false;
            dgvRooms.AllowUserToDeleteRows = false;
            dgvRooms.ReadOnly = true;
            dgvRooms.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvRooms.MultiSelect = false;
            
            // Clear existing columns
            dgvRooms.Columns.Clear();
            
            // Add columns
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RoomName",
                HeaderText = "Tên Phòng",
                DataPropertyName = "room_name",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("Segoe UI", 10, FontStyle.Bold) }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Mô Tả",
                DataPropertyName = "description",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Capacity",
                HeaderText = "Sức Chứa",
                DataPropertyName = "capacity",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "Trạng Thái",
                DataPropertyName = "status",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
            
            dgvRooms.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "Ngày Tạo",
                DataPropertyName = "created_at",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle 
                { 
                    Format = "dd/MM/yyyy HH:mm",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
            
            // Style the DataGridView
            dgvRooms.BackgroundColor = Color.White;
            dgvRooms.BorderStyle = BorderStyle.None;
            dgvRooms.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvRooms.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvRooms.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            dgvRooms.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvRooms.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvRooms.ColumnHeadersHeight = 40;
            dgvRooms.RowTemplate.Height = 35;
            dgvRooms.EnableHeadersVisualStyles = false;
        }

        private void LoadRoomData()
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string statusFilter = cmbStatusFilter.SelectedItem?.ToString() ?? "Tất cả";

                var result = roomService.GetRoomsWithPaging(currentPage, pageSize, searchText, statusFilter);
                dgvRooms.DataSource = result.rooms;

                totalRecords = result.totalRecords;
                totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // Update statistics (get all rooms for accurate stats)
                var allRooms = roomService.GetAllRooms();
                UpdateStatistics(allRooms);

                // Update pagination controls
                UpdatePaginationControls();

                // Apply row colors based on status
                ApplyRowColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdatePaginationControls()
        {
            // Update page info label
            lblPageInfo.Text = $"Trang {currentPage} / {Math.Max(totalPages, 1)} (Tổng: {totalRecords} bản ghi)";

            // Update button states
            btnFirstPage.Enabled = currentPage > 1;
            btnPrevPage.Enabled = currentPage > 1;
            btnNextPage.Enabled = currentPage < totalPages;
            btnLastPage.Enabled = currentPage < totalPages;

            // Update current page textbox
            txtCurrentPage.Text = currentPage.ToString();
        }

        private void UpdateStatistics(System.Collections.Generic.List<room> rooms)
        {
            lblTotalRooms.Text = $"Tổng số phòng: {rooms.Count}";
            lblActiveRooms.Text = $"Hoạt động: {rooms.Count(r => r.status == "Hoạt động")}";
            lblMaintenanceRooms.Text = $"Bảo trì: {rooms.Count(r => r.status == "Bảo trì")}";
        }

        private void ApplyRowColors()
        {
            foreach (DataGridViewRow row in dgvRooms.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "Hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(46, 204, 113);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Bảo trì":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(241, 196, 15);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                        case "Ngừng hoạt động":
                            row.Cells["Status"].Style.BackColor = Color.FromArgb(231, 76, 60);
                            row.Cells["Status"].Style.ForeColor = Color.White;
                            break;
                    }
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadRoomData();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            // TODO: Mở form thêm phòng mới
            MessageBox.Show("Chức năng thêm phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần chỉnh sửa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // TODO: Mở form chỉnh sửa phòng
            MessageBox.Show("Chức năng chỉnh sửa phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvRooms.SelectedRows.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn phòng cần xóa!", "Thông báo", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("Bạn có chắc chắn muốn xóa phòng này?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var selectedRoom = (room)dgvRooms.SelectedRows[0].DataBoundItem;
                    roomService.DeleteRoom(selectedRoom.room_id);
                    
                    MessageBox.Show("Xóa phòng thành công!", "Thành công", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadRoomData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi khi xóa phòng: {ex.Message}", "Lỗi", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentPage = 1; // Reset to first page when filter changes
            LoadRoomData();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            currentPage = 1; // Reset to first page when search changes
            LoadRoomData();
        }

        // Pagination event handlers
        private void btnFirstPage_Click(object sender, EventArgs e)
        {
            currentPage = 1;
            LoadRoomData();
        }

        private void btnPrevPage_Click(object sender, EventArgs e)
        {
            if (currentPage > 1)
            {
                currentPage--;
                LoadRoomData();
            }
        }

        private void btnNextPage_Click(object sender, EventArgs e)
        {
            if (currentPage < totalPages)
            {
                currentPage++;
                LoadRoomData();
            }
        }

        private void btnLastPage_Click(object sender, EventArgs e)
        {
            currentPage = totalPages;
            LoadRoomData();
        }

        private void txtCurrentPage_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (int.TryParse(txtCurrentPage.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= totalPages)
                    {
                        currentPage = pageNumber;
                        LoadRoomData();
                    }
                    else
                    {
                        MessageBox.Show($"Số trang phải từ 1 đến {totalPages}!", "Thông báo",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCurrentPage.Text = currentPage.ToString();
                    }
                }
                else
                {
                    txtCurrentPage.Text = currentPage.ToString();
                }
            }
        }

        private void cmbPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(cmbPageSize.SelectedItem.ToString(), out int newPageSize))
            {
                pageSize = newPageSize;
                currentPage = 1; // Reset to first page when page size changes
                LoadRoomData();
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            roomService?.Dispose();
            base.OnFormClosed(e);
        }

        private void panelControls_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
