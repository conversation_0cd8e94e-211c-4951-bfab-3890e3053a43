using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class AddRoomForm : Form
    {
        private RoomService roomService;
        private user currentUser;
        private List<RoomSectionData> roomSections;

        public AddRoomForm(user loggedInUser)
        {
            InitializeComponent();
            roomService = new RoomService();
            currentUser = loggedInUser;
            roomSections = new List<RoomSectionData>();
            
            InitializeUI();
            AddDefaultSection();
        }

        private void InitializeUI()
        {
            this.Text = "Thêm Phòng Chiếu";
            this.WindowState = FormWindowState.Maximized;
            
            // Setup form events
            this.Load += AddRoomForm_Load;
            
            // Setup control events
            btnAddSection.Click += BtnAddSection_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // Set default status
            cmbStatus.SelectedIndex = 0; // "Hoạt động"
        }

        private void AddRoomForm_Load(object sender, EventArgs e)
        {
            RefreshSectionPreview();
        }

        private void AddDefaultSection()
        {
            var defaultSection = new RoomSectionData
            {
                SectionName = "A",
                Columns = 10,
                Rows = 8,
                VipRows = 2,
                VipStartRow = 1
            };
            roomSections.Add(defaultSection);
            RefreshSectionList();
        }

        private void BtnAddSection_Click(object sender, EventArgs e)
        {
            // Tạo section mới với tên tiếp theo (A, B, C, ...)
            char nextSectionName = (char)('A' + roomSections.Count);
            
            var newSection = new RoomSectionData
            {
                SectionName = nextSectionName.ToString(),
                Columns = 10,
                Rows = 8,
                VipRows = 2,
                VipStartRow = 1
            };
            
            roomSections.Add(newSection);
            RefreshSectionList();
            RefreshSectionPreview();
        }

        private void RefreshSectionList()
        {
            flowLayoutPanelSections.Controls.Clear();
            
            for (int i = 0; i < roomSections.Count; i++)
            {
                var section = roomSections[i];
                var sectionPanel = CreateSectionPanel(section, i);
                flowLayoutPanelSections.Controls.Add(sectionPanel);
            }
        }

        private Panel CreateSectionPanel(RoomSectionData section, int index)
        {
            var panel = new Panel();
            panel.Size = new Size(300, 200);
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.Margin = new Padding(10);
            
            // Section name
            var lblSection = new Label();
            lblSection.Text = $"Section {section.SectionName}";
            lblSection.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblSection.Location = new Point(10, 10);
            lblSection.AutoSize = true;
            
            // Columns
            var lblColumns = new Label();
            lblColumns.Text = "Số cột:";
            lblColumns.Location = new Point(10, 40);
            lblColumns.AutoSize = true;
            
            var numColumns = new NumericUpDown();
            numColumns.Location = new Point(80, 38);
            numColumns.Size = new Size(60, 23);
            numColumns.Minimum = 1;
            numColumns.Maximum = 50;
            numColumns.Value = section.Columns;
            numColumns.ValueChanged += (s, e) => {
                section.Columns = (int)numColumns.Value;
                RefreshSectionPreview(); // Real-time update
            };
            
            // Rows
            var lblRows = new Label();
            lblRows.Text = "Số hàng:";
            lblRows.Location = new Point(160, 40);
            lblRows.AutoSize = true;
            
            var numRows = new NumericUpDown();
            numRows.Location = new Point(220, 38);
            numRows.Size = new Size(60, 23);
            numRows.Minimum = 1;
            numRows.Maximum = 50;
            numRows.Value = section.Rows;
            numRows.ValueChanged += (s, e) => {
                section.Rows = (int)numRows.Value;
                RefreshSectionPreview(); // Real-time update
            };
            
            // VIP rows
            var lblVipRows = new Label();
            lblVipRows.Text = "Số hàng VIP:";
            lblVipRows.Location = new Point(10, 70);
            lblVipRows.AutoSize = true;
            
            var numVipRows = new NumericUpDown();
            numVipRows.Location = new Point(100, 68);
            numVipRows.Size = new Size(60, 23);
            numVipRows.Minimum = 0;
            numVipRows.Maximum = 20;
            numVipRows.Value = section.VipRows;
            numVipRows.ValueChanged += (s, e) => {
                section.VipRows = (int)numVipRows.Value;
                RefreshSectionPreview(); // Real-time update
            };
            
            // VIP start row
            var lblVipStart = new Label();
            lblVipStart.Text = "Hàng VIP bắt đầu:";
            lblVipStart.Location = new Point(10, 100);
            lblVipStart.AutoSize = true;
            
            var numVipStart = new NumericUpDown();
            numVipStart.Location = new Point(130, 98);
            numVipStart.Size = new Size(60, 23);
            numVipStart.Minimum = 1;
            numVipStart.Maximum = 50;
            numVipStart.Value = section.VipStartRow;
            numVipStart.ValueChanged += (s, e) => {
                section.VipStartRow = (int)numVipStart.Value;
                RefreshSectionPreview(); // Real-time update
            };
            
            // Remove button
            if (roomSections.Count > 1)
            {
                var btnRemove = new Guna.UI2.WinForms.Guna2Button();
                btnRemove.Text = "Xóa";
                btnRemove.Size = new Size(60, 30);
                btnRemove.Location = new Point(220, 130);
                btnRemove.FillColor = Color.FromArgb(231, 76, 60);
                btnRemove.Click += (s, e) => {
                    roomSections.RemoveAt(index);
                    RefreshSectionList();
                    RefreshSectionPreview();
                };
                panel.Controls.Add(btnRemove);
            }
            
            panel.Controls.Add(lblSection);
            panel.Controls.Add(lblColumns);
            panel.Controls.Add(numColumns);
            panel.Controls.Add(lblRows);
            panel.Controls.Add(numRows);
            panel.Controls.Add(lblVipRows);
            panel.Controls.Add(numVipRows);
            panel.Controls.Add(lblVipStart);
            panel.Controls.Add(numVipStart);
            
            return panel;
        }

        private void RefreshSectionPreview()
        {
            panelPreview.Controls.Clear();
            
            int startX = 20;
            int currentX = startX;
            
            foreach (var section in roomSections)
            {
                var sectionPreview = CreateSectionPreview(section);
                sectionPreview.Location = new Point(currentX, 20);
                panelPreview.Controls.Add(sectionPreview);
                
                currentX += sectionPreview.Width + 30;
            }
        }

        private Panel CreateSectionPreview(RoomSectionData section)
        {
            var panel = new Panel();
            panel.Size = new Size(section.Columns * 30 + 60, section.Rows * 30 + 80);
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.BackColor = Color.FromArgb(248, 249, 250);
            
            // Section title
            var lblTitle = new Label();
            lblTitle.Text = $"Section {section.SectionName}";
            lblTitle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(52, 73, 94);
            lblTitle.Location = new Point(10, 8);
            lblTitle.AutoSize = true;
            panel.Controls.Add(lblTitle);
            
            // Create seat buttons với naming chính xác
            int seatNumber = 1;
            for (int row = 1; row <= section.Rows; row++)
            {
                for (int col = 1; col <= section.Columns; col++)
                {
                    var btnSeat = new Guna.UI2.WinForms.Guna2Button();
                    btnSeat.Size = new Size(25, 25);
                    btnSeat.Location = new Point(20 + (col - 1) * 30, 35 + (row - 1) * 30);
                    
                    // Seat naming: section_name.001, section_name.002...
                    btnSeat.Text = $"{section.SectionName}.{seatNumber:D3}";
                    btnSeat.Font = new Font("Segoe UI", 7F, FontStyle.Bold);
                    btnSeat.BorderRadius = 5;
                    
                    // Check if VIP seat dựa trên VipStartRow và VipRows
                    bool isVip = row >= section.VipStartRow && row < section.VipStartRow + section.VipRows;
                    
                    if (isVip)
                    {
                        // VIP seat - Luxurious gold gradient
                        btnSeat.FillColor = Color.FromArgb(255, 215, 0);    // Gold
                        btnSeat.FillColor2 = Color.FromArgb(218, 165, 32);  // Goldenrod
                        btnSeat.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
                        btnSeat.ForeColor = Color.FromArgb(139, 69, 19);    // SaddleBrown
                        btnSeat.BorderColor = Color.FromArgb(184, 134, 11); // Dark gold border
                        btnSeat.BorderThickness = 1;
                        
                        // Add shadow effect for luxury look
                        btnSeat.ShadowDecoration.Enabled = true;
                        btnSeat.ShadowDecoration.Color = Color.FromArgb(255, 193, 7);
                        btnSeat.ShadowDecoration.Depth = 3;
                    }
                    else
                    {
                        // Regular seat - Blue gradient
                        btnSeat.FillColor = Color.FromArgb(74, 144, 226);   // Light blue
                        btnSeat.FillColor2 = Color.FromArgb(56, 103, 214);  // Darker blue
                        btnSeat.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
                        btnSeat.ForeColor = Color.White;
                        btnSeat.BorderColor = Color.FromArgb(45, 85, 180);
                        btnSeat.BorderThickness = 1;
                        
                        // Subtle shadow for regular seats
                        btnSeat.ShadowDecoration.Enabled = true;
                        btnSeat.ShadowDecoration.Color = Color.FromArgb(100, 56, 103, 214);
                        btnSeat.ShadowDecoration.Depth = 2;
                    }
                    
                    // Hover effects
                    btnSeat.HoverState.FillColor = isVip ? 
                        Color.FromArgb(255, 235, 59) : 
                        Color.FromArgb(94, 164, 246);
                    
                    panel.Controls.Add(btnSeat);
                    seatNumber++;
                }
            }
            
            // Add legend
            var lblLegend = new Label();
            lblLegend.Text = $"💺 Regular: {section.Columns * (section.Rows - section.VipRows)} | 👑 VIP: {section.Columns * section.VipRows}";
            lblLegend.Font = new Font("Segoe UI", 8F);
            lblLegend.ForeColor = Color.FromArgb(108, 117, 125);
            lblLegend.Location = new Point(10, panel.Height - 25);
            lblLegend.AutoSize = true;
            panel.Controls.Add(lblLegend);
            
            return panel;
        }
    }

    public class RoomSectionData
    {
        public string SectionName { get; set; }
        public int Columns { get; set; }
        public int Rows { get; set; }
        public int VipRows { get; set; }
        public int VipStartRow { get; set; }
    }
}
