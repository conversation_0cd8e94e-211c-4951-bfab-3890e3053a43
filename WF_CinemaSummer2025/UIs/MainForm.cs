using System;
using System.Drawing;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs
{
    public partial class MainForm : Form
    {
        private user currentUser;
        private UserService userService;
        private bool sidebarExpanded = true;
        private const int SIDEBAR_EXPANDED_WIDTH = 250;
        private const int SIDEBAR_COLLAPSED_WIDTH = 60;

        public MainForm(user loggedInUser)
        {
            InitializeComponent();
            currentUser = loggedInUser;
            userService = new UserService();
            
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            
            InitializeUI();
        }

        private void InitializeUI()
        {
            // Set user info
            lblUserName.Text = currentUser.user_name;
            lblUserRole.Text = userService.GetUserRole(currentUser);
            
            // Set role-based permissions
            SetRoleBasedPermissions();
            
            // Initialize sidebar state
            panelSidebar.Width = SIDEBAR_EXPANDED_WIDTH;
            
            // Set form title
            this.Text = $"Cinema Management System - {userService.GetUserRole(currentUser)}";
        }

        private void SetRoleBasedPermissions()
        {
            // Admin có quyền truy cập tất cả
            if (userService.IsAdmin(currentUser))
            {
                btnRoomManagement.Visible = true;
                btnMovieManagement.Visible = true;
            }
            // Staff chỉ có quyền quản lý phim
            else if (userService.IsStaff(currentUser))
            {
                btnRoomManagement.Visible = false;
                btnMovieManagement.Visible = true;
            }
        }

        private void btnToggleSidebar_Click(object sender, EventArgs e)
        {
            ToggleSidebar();
        }

        private void ToggleSidebar()
        {
            Timer timer = new Timer();
            timer.Interval = 10;
            
            int targetWidth = sidebarExpanded ? SIDEBAR_COLLAPSED_WIDTH : SIDEBAR_EXPANDED_WIDTH;
            int step = sidebarExpanded ? -5 : 5;
            
            timer.Tick += (s, e) =>
            {
                panelSidebar.Width += step;
                
                if ((step > 0 && panelSidebar.Width >= targetWidth) || 
                    (step < 0 && panelSidebar.Width <= targetWidth))
                {
                    panelSidebar.Width = targetWidth;
                    timer.Stop();
                    timer.Dispose();
                    
                    sidebarExpanded = !sidebarExpanded;
                    UpdateSidebarLabels();
                }
            };
            
            timer.Start();
        }

        private void UpdateSidebarLabels()
        {
            // Ẩn/hiện text khi sidebar thu nhỏ
            lblUserName.Visible = sidebarExpanded;
            lblUserRole.Visible = sidebarExpanded;
            lblTitle.Visible = sidebarExpanded;
            
            // Cập nhật text của buttons
            if (sidebarExpanded)
            {
                btnRoomManagement.Text = "   Quản lý Phòng";
                btnMovieManagement.Text = "   Quản lý Phim";
                btnLogout.Text = "   Đăng xuất";
            }
            else
            {
                btnRoomManagement.Text = "";
                btnMovieManagement.Text = "";
                btnLogout.Text = "";
            }
        }

        private void btnRoomManagement_Click(object sender, EventArgs e)
        {
            // TODO: Mở form quản lý phòng
            MessageBox.Show("Chức năng Quản lý Phòng sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnMovieManagement_Click(object sender, EventArgs e)
        {
            // TODO: Mở form quản lý phim
            MessageBox.Show("Chức năng Quản lý Phim sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Bạn có chắc chắn muốn đăng xuất?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.Yes)
            {
                this.Hide();
                
                // Mở lại form đăng nhập
                using (var loginForm = new Auth.LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // Cập nhật user mới
                        currentUser = loginForm.LoggedInUser;
                        InitializeUI();
                        this.Show();
                    }
                    else
                    {
                        // Nếu không đăng nhập lại, thoát ứng dụng
                        Application.Exit();
                    }
                }
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("Bạn có chắc chắn muốn thoát ứng dụng?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            userService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
