using System;
  using System.Collections.Generic;
  using System.Linq;
using WF_CinemaSummer2025.Repositories;
using BCrypt.Net;

namespace WF_CinemaSummer2025.Services
{
    public class DatabaseSeeder
    {
        private demo_wfsummerEntities db;

        public DatabaseSeeder()
        {
            db = new demo_wfsummerEntities();
        }

        /// <summary>
        /// Seed dữ liệu mẫu cho bảng users
        /// </summary>
        public void SeedUsers()
        {
            try
            {
                // Test connection first
                if (!db.Database.Exists())
                {
                    System.Windows.Forms.MessageBox.Show("Database không tồn tại hoặc không thể kết nối!", "Lỗi Database",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return;
                }

                // Kiểm tra xem đã có dữ liệu chưa
                if (db.users.Any())
                {
                    return; // Đã có dữ liệu, không cần seed
                }

                // Tạo admin user
                var adminUser = new user
                {
                    user_id = "ADMIN001",
                    user_name = "admin",
                    password = BCrypt.Net.BCrypt.HashPassword("admin123"), // Hash password
                    cccd = "123456789012",
                    roleid = 0, // Admin role
                    isdeleted = false,
                    created_at = DateTime.Now
                };

                // Tạo staff user
                var staffUser = new user
                {
                    user_id = "STAFF001",
                    user_name = "staff",
                    password = BCrypt.Net.BCrypt.HashPassword("staff123"), // Hash password
                    cccd = "987654321098",
                    roleid = 1, // Staff role
                    isdeleted = false,
                    created_at = DateTime.Now
                };

                // Thêm vào database
                db.users.Add(adminUser);
                db.users.Add(staffUser);

                db.SaveChanges();

                System.Windows.Forms.MessageBox.Show("Dữ liệu mẫu đã được tạo!\n\nAdmin - Username: admin, Password: admin123\nStaff - Username: staff, Password: staff123",
                    "Seed Data", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Lỗi khi tạo dữ liệu mẫu:\n{ex.Message}\n\nVui lòng kiểm tra kết nối database!",
                    "Lỗi", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Seed dữ liệu mẫu cho bảng rooms
        /// </summary>
        public void SeedRooms()
        {
            try
            {
                // Kiểm tra xem đã có dữ liệu chưa
                if (db.rooms.Any())
                {
                    return; // Đã có dữ liệu, không cần seed
                }

                var rooms = new List<room>
                {
                    new room
                    {
                        room_id = Guid.NewGuid(),
                        room_name = "Phòng A1",
                        description = "Phòng chiếu tiêu chuẩn với 100 ghế ngồi",
                        status = "Hoạt động",
                        capacity = 100,
                        created_at = DateTime.Now,
                        updated_at = DateTime.Now
                    },
                    new room
                    {
                        room_id = Guid.NewGuid(),
                        room_name = "Phòng A2",
                        description = "Phòng chiếu tiêu chuẩn với 120 ghế ngồi",
                        status = "Hoạt động",
                        capacity = 120,
                        created_at = DateTime.Now,
                        updated_at = DateTime.Now
                    },
                    new room
                    {
                        room_id = Guid.NewGuid(),
                        room_name = "Phòng VIP1",
                        description = "Phòng chiếu VIP với 80 ghế ngồi cao cấp",
                        status = "Hoạt động",
                        capacity = 80,
                        created_at = DateTime.Now,
                        updated_at = DateTime.Now
                    },
                    new room
                    {
                        room_id = Guid.NewGuid(),
                        room_name = "Phòng VIP2",
                        description = "Phòng chiếu VIP với 60 ghế ngồi cao cấp",
                        status = "Hoạt động",
                        capacity = 60,
                        created_at = DateTime.Now,
                        updated_at = DateTime.Now
                    },
                    new room
                    {
                        room_id = Guid.NewGuid(),
                        room_name = "Phòng B1",
                        description = "Phòng chiếu đang bảo trì",
                        status = "Bảo trì",
                        capacity = 90,
                        created_at = DateTime.Now,
                        updated_at = DateTime.Now
                    }
                };

                // Thêm vào database
                foreach (var room in rooms)
                {
                    db.rooms.Add(room);
                }

                db.SaveChanges();

                System.Windows.Forms.MessageBox.Show("Dữ liệu mẫu phòng chiếu đã được tạo!",
                    "Seed Data", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Lỗi khi tạo dữ liệu mẫu phòng:\n{ex.Message}",
                    "Lỗi", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Seed tất cả dữ liệu cần thiết
        /// </summary>
        public void SeedAll()
        {
            SeedUsers();
            SeedRooms();
            // Có thể thêm các seed method khác ở đây
        }

        public void Dispose()
        {
            db?.Dispose();
        }
    }
}
