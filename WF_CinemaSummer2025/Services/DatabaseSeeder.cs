using System;
using System.Linq;
using WF_CinemaSummer2025.Repositories;
using BCrypt.Net;

namespace WF_CinemaSummer2025.Services
{
    public class DatabaseSeeder
    {
        private demo_wfsummerEntities db;

        public DatabaseSeeder()
        {
            db = new demo_wfsummerEntities();
        }

        /// <summary>
        /// Seed dữ liệu mẫu cho bảng users
        /// </summary>
        public void SeedUsers()
        {
            try
            {
                // Kiểm tra xem đã có dữ liệu chưa
                if (db.users.Any())
                {
                    Console.WriteLine("Users table already has data. Skipping seed.");
                    return;
                }

                // Tạo admin user
                var adminUser = new user
                {
                    user_id = "ADMIN001",
                    user_name = "admin",
                    password = BCrypt.Net.BCrypt.HashPassword("admin123"), // Hash password
                    cccd = "123456789012",
                    roleid = 0, // Admin role
                    isdeleted = false,
                    created_at = DateTime.Now
                };

                // Tạo staff user
                var staffUser = new user
                {
                    user_id = "STAFF001",
                    user_name = "staff",
                    password = BCrypt.Net.BCrypt.HashPassword("staff123"), // Hash password
                    cccd = "987654321098",
                    roleid = 1, // Staff role
                    isdeleted = false,
                    created_at = DateTime.Now
                };

                // Thêm vào database
                db.users.Add(adminUser);
                db.users.Add(staffUser);
                
                db.SaveChanges();

                Console.WriteLine("Users seeded successfully!");
                Console.WriteLine("Admin - Username: admin, Password: admin123");
                Console.WriteLine("Staff - Username: staff, Password: staff123");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error seeding users: {ex.Message}");
            }
        }

        /// <summary>
        /// Seed tất cả dữ liệu cần thiết
        /// </summary>
        public void SeedAll()
        {
            SeedUsers();
            // Có thể thêm các seed method khác ở đây
        }

        public void Dispose()
        {
            db?.Dispose();
        }
    }
}
